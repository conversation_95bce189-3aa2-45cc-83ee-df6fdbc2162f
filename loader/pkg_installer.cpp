#include "pkg_installer.h"
#include "../common/lock_ordering.h"
#include "../ps4/ps4_emulator.h"
#include "../ps4/ps4_filesystem.h"
#include "crypto_utils.h"
#include "key_store.h"
#include "self_decrypter.h"
#include <nlohmann/json.hpp>
#include <openssl/aes.h>
#include <openssl/sha.h>
#include <spdlog/spdlog.h>


#ifdef _WIN32
#include <wincrypt.h>
#include <windows.h>
#else
#include <iconv.h>
#endif
#include <algorithm>
#include <cassert>
#include <cctype>
#include <cstring>
#include <filesystem>
#include <iomanip>
#include <iostream>
#include <mutex>
#include <sstream>
#include <thread>

namespace ps4 {

// Placeholder key loading functions. In a real scenario, these would
// interface with a secure keystore to derive content-specific keys.
namespace {
std::unordered_map<uint32_t, std::vector<uint8_t>> g_keyDatabase;
std::unordered_map<uint32_t, std::vector<uint8_t>> g_ivDatabase;
std::mutex g_keyMutex;
bool g_keysInitialized = false;

bool LoadPKGKeys() {
  COMPONENT_LOCK(g_keyMutex, "PKGKeyMutex");
  if (g_keysInitialized)
    return true;

  KeyStore key_store;
  // SECURITY WARNING: This is a placeholder master key. In a production
  // environment, this key must be obtained from a secure source (e.g., hardware
  // module, OS keychain).
  std::string master_key = "your_secure_master_key_32_bytes_long_123";
  if (!key_store.Initialize(master_key)) {
    spdlog::error("Failed to initialize KeyStore: {}",
                  key_store.GetLastError());
    return false;
  }

  // Load PKG keys (key_type 10 is an arbitrary type for PKG keys)
  uint32_t key_type = 10;
  for (uint32_t index = 0; index < 10; ++index) {
    auto key = key_store.GetKey(key_type, index);
    auto iv = key_store.GetIV(key_type, index);
    if (!key.empty() && !iv.empty()) {
      g_keyDatabase[index] = key;
      g_ivDatabase[index] = iv;
      spdlog::debug("Loaded PKG key for index: {}", index);
    }
  }
  g_keysInitialized = true;
  return !g_keyDatabase.empty();
}

const std::vector<uint8_t> &GetPKGKey(uint32_t keyIndex) {
  if (!g_keysInitialized)
    LoadPKGKeys();
  COMPONENT_LOCK(g_keyMutex, "PKGKeyMutex");
  auto it = g_keyDatabase.find(keyIndex);
  if (it != g_keyDatabase.end()) {
    return it->second;
  }
  static const std::vector<uint8_t> kPlaceholderKey(16, 0); // AES-128 key size
  spdlog::warn("Key not found for index {}, using placeholder key", keyIndex);
  return kPlaceholderKey;
}

const std::vector<uint8_t> &GetPKGIV(uint32_t keyIndex) {
  if (!g_keysInitialized)
    LoadPKGKeys();
  COMPONENT_LOCK(g_keyMutex, "PKGKeyMutex");
  auto it = g_ivDatabase.find(keyIndex);
  if (it != g_ivDatabase.end()) {
    return it->second;
  }
  static const std::vector<uint8_t> kPlaceholderIV(16, 0);
  spdlog::warn("IV not found for index {}, using placeholder IV", keyIndex);
  return kPlaceholderIV;
}
} // namespace

PKGInstaller::PKGInstaller(PS4Filesystem *filesystem, PS4Emulator *emulator)
    : m_filesystem(filesystem), m_emulator(emulator), m_installRoot("/app"),
      m_installMutex() {
  if (!filesystem) {
    throw PKGException("PKGInstaller requires a valid PS4Filesystem");
  }

  LoadInstalledPackages();
  spdlog::info("PKGInstaller initialized with filesystem, install root: {}",
               m_installRoot);
}

PKGInstaller::~PKGInstaller() { SaveInstalledPackages(); }

bool PKGInstaller::InstallPKG(const std::string &pkgPath,
                              const std::string &installPath) {
  COMPONENT_LOCK(m_installMutex, "PKGInstallMutex");

  spdlog::info("Starting installation of PKG: {} to {}", pkgPath, installPath);

  try {
    if (!ValidatePKG(pkgPath)) {
      spdlog::error("PKG validation failed - not a valid PS4 PKG file");
      return false;
    }

    std::ifstream file(pkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open PKG file: {}", pkgPath);
      return false;
    }

    PKGHeader header{};
    if (!ReadPKGHeader(file, header)) {
      return false;
    }

    std::vector<PKGItem> items;
    if (!ReadPKGItems(file, header, items)) {
      return false;
    }

    std::vector<uint8_t> nameTable;
    if (!ReadNameTable(file, header, nameTable)) {
      return false;
    }

    std::string contentId(
        header.content_id,
        strnlen(header.content_id, sizeof(header.content_id)));
    std::string titleId =
        contentId.substr(7, 9); // Extract CUSAXXXXX from content ID
    std::string fullInstallPath = installPath + "/" + titleId;

    m_filesystem->CreateVirtualDirectory(fullInstallPath);

    for (const auto &item : items) {
      std::vector<uint8_t> itemData;
      std::string itemName = ExtractSafeFilename(item, nameTable, 0);
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::error("Failed to extract PKG item '{}'", itemName);
        continue;
      }
      std::string virtualPath = fullInstallPath + "/" + itemName;
      m_filesystem->CreateVirtualDirectory(
          std::filesystem::path(virtualPath).parent_path().string());
      if (!m_filesystem->WriteFile(virtualPath, itemData.data(),
                                   itemData.size())) {
        spdlog::error("Failed to write file to virtual filesystem: {}",
                      virtualPath);
      }
    }

    m_installedPackages[contentId] = fullInstallPath;
    SaveInstalledPackages();
    spdlog::info("Successfully installed PKG: {} (Content ID: {})", pkgPath,
                 contentId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Exception during PKG installation: {}", e.what());
    return false;
  }
}

bool PKGInstaller::InstallUpdatePKG(const std::string &updatePkgPath,
                                    const std::string &baseGameContentId) {
  COMPONENT_LOCK(m_installMutex, "PKGInstallMutex");
  spdlog::info("Installing update PKG: {} for base game: {}", updatePkgPath,
               baseGameContentId);

  auto baseIt = m_installedPackages.find(baseGameContentId);
  if (baseIt == m_installedPackages.end()) {
    spdlog::error(
        "Base game with Content ID {} not found. Install the base game first.",
        baseGameContentId);
    return false;
  }
  const std::string &baseGamePath = baseIt->second;

  try {
    std::ifstream file(updatePkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open update PKG file: {}", updatePkgPath);
      return false;
    }

    PKGHeader header{};
    if (!ReadPKGHeader(file, header) || (header.type != PKG_TYPE_PATCH)) {
      spdlog::error("PKG is not a valid update/patch package.");
      return false;
    }

    std::vector<PKGItem> items;
    if (!ReadPKGItems(file, header, items))
      return false;

    std::vector<uint8_t> nameTable;
    if (!ReadNameTable(file, header, nameTable))
      return false;

    for (const auto &item : items) {
      std::vector<uint8_t> itemData;
      std::string itemName = ExtractSafeFilename(item, nameTable, 0);
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::warn("Failed to extract update item '{}'", itemName);
        continue;
      }

      std::string virtualPath = baseGamePath + "/" + itemName;
      m_filesystem->CreateVirtualDirectory(
          std::filesystem::path(virtualPath).parent_path().string());
      if (!m_filesystem->WriteFile(virtualPath, itemData.data(),
                                   itemData.size())) {
        spdlog::error("Failed to write updated file: {}", virtualPath);
      } else {
        spdlog::info("Updated/Added file: {}", virtualPath);
      }
    }

    spdlog::info("Successfully installed update PKG for {}", baseGameContentId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Exception during update PKG installation: {}", e.what());
    return false;
  }
}

bool PKGInstaller::InstallDlcPKG(const std::string &dlcPkgPath,
                                 const std::string &baseGameContentId) {
  COMPONENT_LOCK(m_installMutex, "PKGInstallMutex");
  spdlog::info("Installing DLC PKG: {} for base game: {}", dlcPkgPath,
               baseGameContentId);

  auto baseIt = m_installedPackages.find(baseGameContentId);
  if (baseIt == m_installedPackages.end()) {
    spdlog::error(
        "Base game with Content ID {} not found for DLC installation.",
        baseGameContentId);
    return false;
  }
  const std::string &baseGamePath = baseIt->second;

  try {
    std::ifstream file(dlcPkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open DLC PKG file: {}", dlcPkgPath);
      return false;
    }

    PKGHeader header{};
    std::string dlcContentId, version, title;
    if (!GetPKGInfo(dlcPkgPath, dlcContentId, version, title) ||
        header.type != PKG_TYPE_ADDON) {
      spdlog::error("PKG is not a valid DLC/Addon package.");
      return false;
    }

    // DLC is installed in the 'addcont' directory inside the base game's path
    std::string dlcInstallRoot = baseGamePath + "/addcont/" + dlcContentId;
    m_filesystem->CreateVirtualDirectory(dlcInstallRoot);

    file.clear();
    file.seekg(0);
    ReadPKGHeader(file, header);

    std::vector<PKGItem> items;
    ReadPKGItems(file, header, items);

    std::vector<uint8_t> nameTable;
    ReadNameTable(file, header, nameTable);

    for (const auto &item : items) {
      std::vector<uint8_t> itemData;
      std::string itemName = ExtractSafeFilename(item, nameTable, 0);
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::warn("Failed to extract DLC item '{}'", itemName);
        continue;
      }
      std::string virtualPath = dlcInstallRoot + "/" + itemName;
      m_filesystem->CreateVirtualDirectory(
          std::filesystem::path(virtualPath).parent_path().string());
      if (!m_filesystem->WriteFile(virtualPath, itemData.data(),
                                   itemData.size())) {
        spdlog::error("Failed to write DLC file: {}", virtualPath);
      }
    }

    m_installedPackages[dlcContentId] = dlcInstallRoot;
    SaveInstalledPackages();
    spdlog::info("Successfully installed DLC PKG {} for {}", dlcContentId,
                 baseGameContentId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Exception during DLC PKG installation: {}", e.what());
    return false;
  }
}

bool PKGInstaller::ValidatePKG(const std::string &pkgPath) {
  spdlog::info("Validating PKG: {}", pkgPath);

  std::ifstream file(pkgPath, std::ios::binary | std::ios::ate);
  if (!file.is_open()) {
    spdlog::error("Failed to open PKG file: {}", pkgPath);
    return false;
  }

  uint64_t fileSize = file.tellg();
  file.seekg(0, std::ios::beg);

  PKGHeader header{};
  if (!ReadPKGHeader(file, header)) {
    spdlog::error("Invalid PKG header");
    return false;
  }

  // A more robust check for various header fields
  if (header.table_offset >= fileSize || header.body_offset >= fileSize ||
      header.content_offset >= fileSize ||
      header.content_offset + header.content_size > fileSize) {
    spdlog::error("PKG header offsets/sizes are out of bounds.");
    return false;
  }

  // In a real implementation, we would verify signatures here
  spdlog::info("PKG validation passed (basic structural validation only)");
  return true;
}

bool PKGInstaller::ExtractPKG(const std::string &pkgPath,
                              const std::string &extractPath) {
  spdlog::info("Extracting PKG: {} to {}", pkgPath, extractPath);

  try {
    std::filesystem::create_directories(extractPath);

    std::ifstream file(pkgPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open PKG file: {}", pkgPath);
      return false;
    }

    PKGHeader header{};
    if (!ReadPKGHeader(file, header)) {
      return false;
    }

    std::vector<PKGItem> items;
    if (!ReadPKGItems(file, header, items)) {
      return false;
    }

    std::vector<uint8_t> nameTable;
    if (!ReadNameTable(file, header, nameTable)) {
      return false;
    }

    for (size_t i = 0; i < items.size(); i++) {
      const PKGItem &item = items[i];
      std::vector<uint8_t> itemData;
      std::string itemName =
          ExtractSafeFilename(item, nameTable, static_cast<int>(i));
      if (!ExtractPKGItem(file, item, nameTable, itemData)) {
        spdlog::warn("Failed to extract item '{}'", itemName);
        continue;
      }

      std::string outputPath = extractPath + "/" + itemName;
      std::filesystem::create_directories(
          std::filesystem::path(outputPath).parent_path());
      std::ofstream outFile(outputPath, std::ios::binary);
      if (outFile.is_open()) {
        outFile.write(reinterpret_cast<const char *>(itemData.data()),
                      itemData.size());
      } else {
        spdlog::error("Failed to write extracted file: {}", outputPath);
      }
    }

    spdlog::info("Successfully extracted PKG to: {}", extractPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Exception during PKG extraction: {}", e.what());
    return false;
  }
}

bool PKGInstaller::GetPKGInfo(const std::string &pkgPath,
                              std::string &contentId, std::string &version,
                              std::string &title) {
  std::ifstream file(pkgPath, std::ios::binary);
  if (!file.is_open()) {
    spdlog::error("Failed to open PKG file: {}", pkgPath);
    return false;
  }

  PKGHeader header{};
  if (!ReadPKGHeader(file, header)) {
    return false;
  }

  contentId = std::string(
      header.content_id, strnlen(header.content_id, sizeof(header.content_id)));

  std::vector<PKGItem> items;
  if (!ReadPKGItems(file, header, items)) {
    return false;
  }

  std::vector<uint8_t> nameTable;
  if (!ReadNameTable(file, header, nameTable)) {
    return false;
  }

  for (const auto &item : items) {
    std::string itemName = ExtractSafeFilename(item, nameTable, 0);
    if (itemName == "param.sfo") {
      std::vector<uint8_t> sfoData;
      if (ExtractPKGItem(file, item, nameTable, sfoData)) {
        std::unordered_map<std::string, std::string> sfoParams;
        if (ParseSFO(sfoData, sfoParams)) {
          version = sfoParams.count("APP_VER") ? sfoParams["APP_VER"] : "N/A";
          title = sfoParams.count("TITLE") ? sfoParams["TITLE"] : "N/A";
        }
      }
      break;
    }
  }

  return !contentId.empty();
}

std::vector<std::string> PKGInstaller::ListInstalledPackages() const {
  std::vector<std::string> packages;
  COMPONENT_LOCK(const_cast<std::mutex &>(m_installMutex), "PKGInstallMutex");
  packages.reserve(m_installedPackages.size());

  for (const auto &[contentId, path] : m_installedPackages) {
    packages.push_back(contentId);
  }

  return packages;
}

bool PKGInstaller::UninstallPackage(const std::string &contentId) {
  COMPONENT_LOCK(m_installMutex, "PKGInstallMutex");
  auto it = m_installedPackages.find(contentId);
  if (it == m_installedPackages.end()) {
    spdlog::error("Package not found: {}", contentId);
    return false;
  }

  spdlog::info("Uninstalling package: {} from {}", contentId, it->second);
  bool success = m_filesystem->RemoveDirectory(it->second);
  if (success) {
    m_installedPackages.erase(it);
    SaveInstalledPackages();
    spdlog::info("Successfully uninstalled package: {}", contentId);
  } else {
    spdlog::error("Failed to remove package directory: {}", it->second);
  }
  return success;
}

bool PKGInstaller::ReadPKGHeader(std::ifstream &file, PKGHeader &header) {
  file.seekg(0, std::ios::beg);
  file.read(reinterpret_cast<char *>(&header), sizeof(PKGHeader));

  if (!file.good()) {
    spdlog::error("Failed to read PKG header - file I/O error");
    return false;
  }

  // Convert all multi-byte fields from big-endian to host byte order
  header.magic = be32toh(header.magic);
  header.type = be32toh(header.type);
  header.file_count = be32toh(header.file_count);
  header.entry_count = be32toh(header.entry_count);
  header.table_offset = be32toh(header.table_offset);
  header.body_offset = be64toh(header.body_offset);
  header.body_size = be64toh(header.body_size);
  header.content_offset = be64toh(header.content_offset);
  header.content_size = be64toh(header.content_size);

  if (header.magic != PKG_MAGIC) {
    spdlog::error("Invalid PKG magic number: got 0x{:08X}", header.magic);
    return false;
  }

  return true;
}

bool PKGInstaller::ReadPKGItems(std::ifstream &file, const PKGHeader &header,
                                std::vector<PKGItem> &items) {
  if (header.entry_count == 0)
    return true;

  // Critical bounds checking for PKG entry count
  static constexpr uint32_t MAX_PKG_ENTRIES = 100000; // Reasonable upper limit
  static constexpr uint32_t SANE_PKG_ENTRIES = 10000; // Warn above this

  if (header.entry_count > MAX_PKG_ENTRIES) {
    spdlog::error("ReadPKGItems: Entry count {} exceeds maximum {}",
                  header.entry_count, MAX_PKG_ENTRIES);
    return false;
  }

  if (header.entry_count > SANE_PKG_ENTRIES) {
    spdlog::warn("ReadPKGItems: Unusually high entry count: {}",
                 header.entry_count);
  }

  // Validate table offset
  if (header.table_offset == 0) {
    spdlog::error("ReadPKGItems: Invalid table offset 0");
    return false;
  }

  std::vector<PKGTableEntry> table_entries;

  // Safe vector allocation with exception handling
  try {
    table_entries.reserve(header.entry_count);
    table_entries.resize(header.entry_count);
  } catch (const std::bad_alloc &e) {
    spdlog::error("ReadPKGItems: Failed to allocate memory for {} entries: {}",
                  header.entry_count, e.what());
    return false;
  }

  file.seekg(header.table_offset, std::ios::beg);
  if (!file.good()) {
    spdlog::error("ReadPKGItems: Failed to seek to table offset 0x{:x}",
                  header.table_offset);
    return false;
  }

  file.read(reinterpret_cast<char *>(table_entries.data()),
            sizeof(PKGTableEntry) * header.entry_count);
  if (!file.good())
    return false;

  items.clear();
  items.reserve(header.entry_count);

  for (const auto &entry : table_entries) {
    PKGItem item;
    item.nameOffset = be32toh(entry.filename_offset);
    item.dataOffset = header.body_offset + be32toh(entry.offset);
    item.dataSize = be32toh(entry.size);
    item.flags = static_cast<uint8_t>(be32toh(entry.flags1));
    item.key_index = (be32toh(entry.flags2) >> 8) & 0xF;
    items.push_back(item);
  }

  return true;
}

bool PKGInstaller::ReadNameTable(std::ifstream &file, const PKGHeader &header,
                                 std::vector<uint8_t> &nameTable) {
  uint64_t nameTableOffset =
      header.table_offset + (sizeof(PKGTableEntry) * header.entry_count);
  uint64_t nameTableSize = header.body_offset - nameTableOffset;

  if (nameTableSize > 10 * 1024 * 1024) { // Sanity check: 10MB
    spdlog::error("Name table size is unreasonably large: {}", nameTableSize);
    return false;
  }

  nameTable.resize(nameTableSize);
  file.seekg(nameTableOffset, std::ios::beg);
  if (!file.good())
    return false;

  file.read(reinterpret_cast<char *>(nameTable.data()), nameTableSize);
  return file.good();
}

bool PKGInstaller::ExtractPKGItem(std::ifstream &file, const PKGItem &item,
                                  const std::vector<uint8_t> &nameTable,
                                  std::vector<uint8_t> &data) {
  if (item.dataSize == 0) {
    data.clear();
    return true;
  }

  data.resize(item.dataSize);
  file.seekg(item.dataOffset, std::ios::beg);
  if (!file.good())
    return false;

  file.read(reinterpret_cast<char *>(data.data()), item.dataSize);
  if (!file.good())
    return false;

  std::string itemName = ExtractSafeFilename(item, nameTable, 0);

  // Handle encrypted PKG items (AES-128-CBC is standard)
  if (item.flags & 0x80000000) {
    spdlog::info("Encrypted PKG item detected: '{}'", itemName);
    const auto &key = GetPKGKey(item.key_index);
    const auto &iv = GetPKGIV(item.key_index);

    if (key.size() != 16) {
      spdlog::error(
          "Invalid key size for AES-128 decryption: {}. Need 16 bytes.",
          key.size());
      return false;
    }

    std::array<uint8_t, 16> key_array;
    std::array<uint8_t, 16> iv_array;
    std::copy(key.begin(), key.end(), key_array.begin());
    std::copy(iv.begin(), iv.end(), iv_array.begin());

    std::vector<uint8_t> decryptedData;
    if (!ps4::AESCrypto::DecryptAES128_CBC(data, key_array, iv_array,
                                           decryptedData)) {
      spdlog::error("Failed to decrypt PKG item '{}'", itemName);
      return false;
    }
    data = std::move(decryptedData);
  }

  // Handle SELF files
  if (itemName == "eboot.bin" || itemName.find(".self") != std::string::npos) {
    if (SelfDecrypter::IsEncryptedSelf(data)) {
      spdlog::info("Detected encrypted SELF file: {}, attempting decryption",
                   itemName);
      data = SelfDecrypter::DecryptSelf(data);
      if (data.empty()) {
        spdlog::error("Failed to decrypt SELF file '{}'", itemName);
        return false;
      }
    }
  }
  return true;
}

std::string
PKGInstaller::ExtractSafeFilename(const PKGItem &item,
                                  const std::vector<uint8_t> &nameTable,
                                  int fallbackIndex) {
  if (item.nameOffset >= nameTable.size()) {
    return "file_" + std::to_string(fallbackIndex);
  }

  const char *name_ptr =
      reinterpret_cast<const char *>(&nameTable[item.nameOffset]);
  size_t max_len = nameTable.size() - item.nameOffset;
  size_t len = strnlen(name_ptr, max_len);

  if (len == 0)
    return "file_" + std::to_string(fallbackIndex);

  std::string filename(name_ptr, len);

  // Sanitize filename
  std::replace(filename.begin(), filename.end(), '\\', '/');
  // Remove other invalid characters if necessary

  return filename;
}

const std::string &
PKGInstaller::GetInstallPath(const std::string &contentId) const noexcept {
  COMPONENT_LOCK(const_cast<std::mutex &>(m_installMutex), "PKGInstallMutex");
  auto it = m_installedPackages.find(contentId);
  if (it != m_installedPackages.end()) {
    return it->second;
  }
  static const std::string emptyPath;
  return emptyPath;
}

bool PKGInstaller::LoadInstalledPackages() {
  COMPONENT_LOCK(m_installMutex, "PKGInstallMutex");
  m_installedPackages.clear();
  std::string configPath = "/system/packages.json";
  std::vector<uint8_t> jsonData;

  if (!m_filesystem->ReadFile(configPath, jsonData) || jsonData.empty()) {
    // Create empty packages.json if it doesn't exist
    spdlog::info("Creating empty packages.json file");
    m_filesystem->CreateVirtualDirectory("/system");
    nlohmann::json emptyJson = nlohmann::json::object();
    std::string jsonStr = emptyJson.dump(2);
    if (!m_filesystem->WriteFile("/system/packages.json",
                                 (const uint8_t *)jsonStr.c_str(),
                                 jsonStr.size())) {
      spdlog::warn("Failed to create packages.json file, continuing with empty package list");
    }
    return true;
  }

  try {
    auto json = nlohmann::json::parse(jsonData);
    for (auto &[contentId, path] : json.items()) {
      m_installedPackages[contentId] = path.get<std::string>();
    }
    spdlog::info("Loaded {} installed packages from packages.json", m_installedPackages.size());
  } catch (const std::exception &e) {
    spdlog::error("Error parsing packages database: {}", e.what());
    return false;
  }
  return true;
}

bool PKGInstaller::SaveInstalledPackages() const {
  COMPONENT_LOCK(const_cast<std::mutex &>(m_installMutex), "PKGInstallMutex");
  nlohmann::json json(m_installedPackages);
  std::string jsonStr = json.dump(2);
  m_filesystem->CreateVirtualDirectory("/system");
  return m_filesystem->WriteFile("/system/packages.json",
                                 (const uint8_t *)jsonStr.c_str(),
                                 jsonStr.size());
}

bool PKGInstaller::ParseSFO(
    const std::vector<uint8_t> &sfoData,
    std::unordered_map<std::string, std::string> &params) {
  if (sfoData.size() < sizeof(SFOHeader))
    return false;

  const auto *header = reinterpret_cast<const SFOHeader *>(sfoData.data());
  if (header->magic != SFO_MAGIC)
    return false;

  const auto *entries =
      reinterpret_cast<const SFOParam *>(sfoData.data() + sizeof(SFOHeader));
  const char *key_table = reinterpret_cast<const char *>(
      sfoData.data() + be32toh(header->keyTableOffset));
  const char *data_table = reinterpret_cast<const char *>(
      sfoData.data() + be32toh(header->dataTableOffset));

  for (uint32_t i = 0; i < be32toh(header->paramCount); ++i) {
    const auto &entry = entries[i];
    std::string key = key_table + be16toh(entry.keyOffset);
    std::string value(data_table + be32toh(entry.dataOffset),
                      be32toh(entry.length));
    // Remove null terminator from value if present
    if (!value.empty() && value.back() == '\0') {
      value.pop_back();
    }
    params[key] = value;
  }
  return true;
}

// Other private methods (CreateInstallDirectory, MergeUpdateFiles, etc.) remain
// largely the same, but would be fortified with more detailed logging and error
// checking in a production scenario.
#pragma region Helper Implementations
std::string PKGInstaller::CreateInstallDirectory(const std::string &basePath,
                                                 const std::string &contentId) {
  std::string titleId = contentId.substr(7, 9);
  std::string installPath = basePath + "/" + titleId;
  m_filesystem->CreateVirtualDirectory(installPath);
  return installPath;
}

bool PKGInstaller::MergeUpdateFiles(
    std::unordered_map<std::string, std::vector<uint8_t>> &baseFiles,
    const std::unordered_map<std::string, std::vector<uint8_t>> &updateFiles) {
  for (const auto &[filename, data] : updateFiles) {
    baseFiles[filename] = data;
  }
  return true;
}

std::string PKGInstaller::ComputeSHA256(const std::vector<uint8_t> &data) {
  unsigned char hash[SHA256_DIGEST_LENGTH];
  SHA256_CTX sha256;
  SHA256_Init(&sha256);
  SHA256_Update(&sha256, data.data(), data.size());
  SHA256_Final(hash, &sha256);
  std::stringstream ss;
  for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
    ss << std::hex << std::setw(2) << std::setfill('0')
       << static_cast<int>(hash[i]);
  }
  return ss.str();
}

bool PKGInstaller::VerifyPackageIntegrity(
    const PKGHeader &header, const std::vector<uint8_t> &packageData) {
  // Basic structural validation
  if (header.body_offset + header.body_size > packageData.size()) {
    spdlog::error("PKG body is out of bounds.");
    return false;
  }
  // A real implementation would verify cryptographic signatures here
  return true;
}
#pragma endregion

} // namespace ps4