#include "ps4_audio.h"
#include "../memory/memory_diagnostics.h"
#include "ps4_emulator.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <fstream>
#include <fmt/format.h>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <thread>
#include <vector>
#include <queue>
#include <mutex>
#include <shared_mutex>
#include <atomic>
#include <memory>

// PortAudio include
#include <portaudio.h>

// FFmpeg includes
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libswresample/swresample.h>
#include <libavutil/channel_layout.h>
}

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Use the ps4 namespace for all definitions in this file
namespace ps4 {


/**
 * @brief Exports a crash report for audio initialization failure.
 */
void ExportCrashReport(const std::string &filename);

/**
 * @brief Structure for audio device information.
 */
struct DeviceInfo {
  PaDeviceIndex index;
  std::string name;
  bool isBluetooth;
  PaHostApiTypeId hostApi;
  int maxOutputChannels;
  double defaultSampleRate;
};

/**
 * @brief Constructs the audio device.
 */
AudioDevice::AudioDevice() {
  auto start = std::chrono::steady_clock::now();
  audioStream_ = {nullptr, SAMPLE_RATE, 2, 1.0f, false, {}};
  audioStream_.lastUnderrunWarningTime = std::chrono::steady_clock::now();
  stats_ = Stats();
  spdlog::info("AudioDevice constructed");
  auto end = std::chrono::steady_clock::now();
  stats_.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
AudioDevice::~AudioDevice() {
  auto start = std::chrono::steady_clock::now();
  Stop();
  spdlog::info("AudioDevice destroyed");
  auto end = std::chrono::steady_clock::now();
  stats_.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief PortAudio stream callback.
 */
int AudioDevice::StreamCallback(const void *input, void *output,
                                unsigned long frameCount,
                                const PaStreamCallbackTimeInfo *timeInfo,
                                PaStreamCallbackFlags statusFlags,
                                void *userData) {
  auto start = std::chrono::steady_clock::now();
  AudioDevice *device = static_cast<AudioDevice *>(userData);
  std::unique_lock<std::shared_mutex> lock(device->streamMutex_);
  AudioStream *stream = &device->audioStream_;
  try {
    if (!stream || !stream->isPlaying) {
      spdlog::trace("Audio stream not playing");
      std::memset(output, 0, frameCount * stream->numChannels * sizeof(float));
      stream->cacheHits++;
      device->stats_.cacheHits++;
      return paContinue;
    }
    float *out = static_cast<float *>(output);
    size_t framesToCopy = frameCount;
    if (stream->buffers.empty()) {
      stream->bufferUnderruns++;
      device->stats_.bufferUnderruns++;

      // Time-based throttling for underrun warnings
      if (device->underrunWarningIntervalSeconds_ > 0) {
        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastWarning = std::chrono::duration_cast<std::chrono::seconds>(
            now - stream->lastUnderrunWarningTime).count();

        if (timeSinceLastWarning >= device->underrunWarningIntervalSeconds_) {
          spdlog::warn("Audio buffer underrun, count={}, queue_size={}, time_since_last={}s",
                       stream->bufferUnderruns, stream->buffers.size(), timeSinceLastWarning);
          stream->lastUnderrunWarningTime = now;
        }
      }

      std::memset(out, 0, frameCount * stream->numChannels * sizeof(float));
      stream->cacheMisses++;
      device->stats_.cacheMisses++;
      return paContinue;
    }
    size_t availableFrames =
        stream->buffers.front().size() / stream->numChannels;
    if (availableFrames < framesToCopy) {
      stream->bufferUnderruns++;
      device->stats_.bufferUnderruns++;
      spdlog::warn("Audio underflow: available={} frames, requested={}",
                   availableFrames, framesToCopy);
      framesToCopy = availableFrames;
    }
    for (size_t i = 0; i < framesToCopy * stream->numChannels; ++i) {
      out[i] = stream->buffers.front()[i] * stream->volume;
    }
    if (framesToCopy < frameCount) {
      std::memset(out + framesToCopy * stream->numChannels, 0,
                  (frameCount - framesToCopy) * stream->numChannels *
                      sizeof(float));
    }
    stream->buffers.pop();
    stream->cacheHits++;
    device->stats_.cacheHits++;
    device->stats_.bufferQueueSize = stream->buffers.size();
    auto end = std::chrono::steady_clock::now();
    device->stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return paContinue;
  } catch (const std::exception &e) {
    spdlog::error("Stream callback failed: {}", e.what());
    stream->cacheMisses++;
    device->stats_.cacheMisses++;
    return paContinue;
  }
}

/**
 * @brief Enumerates audio devices with timeout and filtering.
 */
static std::vector<DeviceInfo>
EnumerateDevices(const std::chrono::milliseconds &timeout) {
  auto start = std::chrono::steady_clock::now();
  std::vector<DeviceInfo> devices;

  int numDevices = Pa_GetDeviceCount();
  if (numDevices < 0) {
    spdlog::error("Pa_GetDeviceCount failed: {}", Pa_GetErrorText(numDevices));
    return devices;
  }

  for (int i = 0; i < numDevices; ++i) {
    if (std::chrono::steady_clock::now() - start > timeout) {
      spdlog::error("Audio device enumeration timed out after {}ms",
                    timeout.count());
      break;
    }

    auto queryStart = std::chrono::steady_clock::now();
    const PaDeviceInfo *info = Pa_GetDeviceInfo(i);
    auto queryDuration =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - queryStart)
            .count();
    if (!info) {
      spdlog::warn("Failed to get device info for index {} after {}ms", i,
                   queryDuration);
      continue;
    }

    const PaHostApiInfo *apiInfo = Pa_GetHostApiInfo(info->hostApi);
    if (!apiInfo) {
      spdlog::warn("Failed to get host API info for device {} after {}ms", i,
                   queryDuration);
      continue;
    }

    if (info->maxOutputChannels <= 0) {
      spdlog::debug("Skipping device {}: no output channels", i);
      continue;
    }

    std::string name = info->name;
    bool isBluetooth = name.find("Bluetooth") != std::string::npos ||
                       name.find("Hands-Free") != std::string::npos ||
                       name.find("bthhfenum") != std::string::npos;

    DeviceInfo device;
    device.index = i;
    device.name = name;
    device.isBluetooth = isBluetooth;
    device.hostApi = apiInfo->type;
    device.maxOutputChannels = info->maxOutputChannels;
    device.defaultSampleRate = info->defaultSampleRate;

    devices.push_back(device);
    spdlog::debug("Enumerated device {}: name={}, hostApi={}, channels={}, "
                  "sampleRate={}, isBluetooth={}",
                  i, name, static_cast<int>(apiInfo->type),
                  info->maxOutputChannels, info->defaultSampleRate,
                  isBluetooth);
  }

  return devices;
}

/**
 * @brief Selects a suitable audio device.
 */
static DeviceInfo SelectDevice(const std::vector<DeviceInfo> &devices) {
  if (devices.empty()) {
    throw AudioException("No audio devices available");
  }

  // Prefer WASAPI non-Bluetooth device
  for (const auto &device : devices) {
    if (device.hostApi == paWASAPI && !device.isBluetooth) {
      return device;
    }
  }

  // Fallback to default output device
  PaDeviceIndex defaultDevice = Pa_GetDefaultOutputDevice();
  for (const auto &device : devices) {
    if (device.index == defaultDevice && !device.isBluetooth) {
      return device;
    }
  }

  // Fallback to first non-Bluetooth device
  for (const auto &device : devices) {
    if (!device.isBluetooth) {
      return device;
    }
  }

  // Last resort: first device
  spdlog::warn("Using first available device: {}", devices.front().name);
  return devices.front();
}

/**
 * @brief Initializes the audio device with timeout and device filtering.
 */
bool AudioDevice::Initialize(PaDeviceIndex deviceIndex) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (initialized_) {
      spdlog::warn("AudioDevice already initialized");
      return true;
    }

    // Initialize PortAudio with proper timeout using async execution
    const auto initTimeout = std::chrono::milliseconds(3000);
    spdlog::info("Starting PortAudio initialization with {}ms timeout", initTimeout.count());

    std::atomic<bool> initComplete{false};
    std::atomic<PaError> initResult{paNoError};
    std::exception_ptr initException = nullptr;

    // Run Pa_Initialize in a separate thread to avoid hanging the main thread
    std::thread initThread([&]() {
      try {
        auto threadStart = std::chrono::steady_clock::now();
        PaError err = Pa_Initialize();
        auto threadEnd = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(threadEnd - threadStart);
        spdlog::debug("Pa_Initialize completed in {}ms with result: {}", duration.count(), err);
        initResult.store(err);
      } catch (...) {
        initException = std::current_exception();
      }
      initComplete.store(true);
    });

    // Wait for initialization with timeout
    auto initStart = std::chrono::steady_clock::now();
    while (!initComplete.load() &&
           (std::chrono::steady_clock::now() - initStart) < initTimeout) {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    if (!initComplete.load()) {
      spdlog::error("PortAudio initialization timed out after {}ms - continuing without audio",
                    initTimeout.count());
      // Detach the thread since we can't safely terminate it
      initThread.detach();
      // Initialize in null mode (no audio output)
      initialized_ = true;
      audioStream_.stream = nullptr;
      stats_.cacheHits++;
      spdlog::warn("Audio system initialized in null mode due to timeout");
      return true;
    }

    // Join the thread now that it's complete
    initThread.join();

    // Check for exceptions
    if (initException) {
      std::rethrow_exception(initException);
    }

    // Check the result
    PaError err = initResult.load();
    if (err != paNoError) {
      spdlog::error("PortAudio initialization failed: {}", Pa_GetErrorText(err));
      // Continue without audio instead of failing completely
      spdlog::warn("Continuing without audio due to PortAudio failure");
      initialized_ = true;
      audioStream_.stream = nullptr;
      stats_.cacheHits++;
      return true;
    }

    spdlog::info("PortAudio initialized successfully");

    // Enumerate devices
    std::vector<DeviceInfo> devices =
        EnumerateDevices(std::chrono::milliseconds(5000));
    if (devices.empty()) {
      spdlog::warn("No audio devices found, using null output");
      initialized_ = true;
      audioStream_.stream = nullptr;
      stats_.cacheHits++;
      return true;
    }

    // Select device
    DeviceInfo selectedDevice =
        (deviceIndex == -1) ? SelectDevice(devices) : devices[deviceIndex];
    spdlog::info("Selected audio device: {} (index={}, hostApi={})",
                 selectedDevice.name, selectedDevice.index,
                 static_cast<int>(selectedDevice.hostApi));

    // Configure stream parameters
    PaStreamParameters outputParams = {};
    outputParams.device = selectedDevice.index;
    outputParams.channelCount =
        std::min(audioStream_.numChannels, selectedDevice.maxOutputChannels);
    outputParams.sampleFormat = paFloat32;
    outputParams.suggestedLatency =
        Pa_GetDeviceInfo(selectedDevice.index)->defaultLowOutputLatency;
    outputParams.hostApiSpecificStreamInfo = nullptr;

    // Open stream with timeout protection
    spdlog::debug("Opening audio stream...");
    const auto streamTimeout = std::chrono::milliseconds(2000);

    std::atomic<bool> streamComplete{false};
    std::atomic<PaError> streamResult{paNoError};
    std::exception_ptr streamException = nullptr;

    std::thread streamThread([&]() {
      try {
        PaError err = Pa_OpenStream(&audioStream_.stream, nullptr, &outputParams,
                            selectedDevice.defaultSampleRate, FRAMES_PER_BUFFER,
                            paClipOff, StreamCallback, this);
        streamResult.store(err);
      } catch (...) {
        streamException = std::current_exception();
      }
      streamComplete.store(true);
    });

    auto streamStart = std::chrono::steady_clock::now();
    while (!streamComplete.load() &&
           (std::chrono::steady_clock::now() - streamStart) < streamTimeout) {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    if (!streamComplete.load()) {
      spdlog::warn("Stream opening timed out after {}ms - continuing without audio",
                   streamTimeout.count());
      streamThread.detach();
      Pa_Terminate();
      initialized_ = true;
      audioStream_.stream = nullptr;
      stats_.cacheHits++;
      return true;
    }

    streamThread.join();

    if (streamException) {
      std::rethrow_exception(streamException);
    }

    err = streamResult.load();
    if (err != paNoError) {
      spdlog::warn("PortAudio open stream failed: {} - continuing without audio",
                   Pa_GetErrorText(err));
      Pa_Terminate();
      initialized_ = true;
      audioStream_.stream = nullptr;
      stats_.cacheHits++;
      return true;
    }

    spdlog::info("Audio stream opened successfully");

    initialized_ = true;
    stats_.numChannels = outputParams.channelCount;
    audioStream_.sampleRate = selectedDevice.defaultSampleRate;
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("AudioDevice initialized with device {} (name={})",
                 selectedDevice.index, selectedDevice.name);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("AudioDevice initialization failed: {}", e.what());
    if (audioStream_.stream) {
      Pa_CloseStream(audioStream_.stream);
      audioStream_.stream = nullptr;
    }
    Pa_Terminate();
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_init_error.json");

    // Continue without audio instead of failing completely
    spdlog::warn("Continuing without audio due to initialization failure");
    initialized_ = true;
    audioStream_.stream = nullptr;
    stats_.cacheHits++;
    return true;
  }
}

/**
 * @brief Exports a crash report for audio initialization failure.
 */
void ExportCrashReport(const std::string &filename) {
  nlohmann::json report;
  report["timestamp"] =
      std::chrono::system_clock::now().time_since_epoch().count();
  report["error"] = "AudioDevice initialization failure";
  report["devices"] = nlohmann::json::array();
  int numDevices = Pa_GetDeviceCount();
  for (int i = 0; i < numDevices; ++i) {
    const PaDeviceInfo *info = Pa_GetDeviceInfo(i);
    if (info) {
      nlohmann::json device;
      device["index"] = i;
      device["name"] = info->name;
      device["hostApi"] =
          Pa_GetHostApiInfo(info->hostApi) ? Pa_GetHostApiInfo(info->hostApi)->type : -1;
      device["maxOutputChannels"] = info->maxOutputChannels;
      report["devices"].push_back(device);
    }
  }
  std::ofstream out(filename);
  out << report.dump(2);
  spdlog::info("Crash report exported to {}", filename);
}

/**
 * @brief Starts audio playback.
 */
void AudioDevice::Start() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (initialized_ && audioStream_.stream && !audioStream_.isPlaying) {
      PaError err = Pa_StartStream(audioStream_.stream);
      if (err != paNoError) {
        spdlog::error("PortAudio start stream failed: {}", Pa_GetErrorText(err));
        throw AudioException("PortAudio stream start failed: " +
                             std::string(Pa_GetErrorText(err)));
      }
      audioStream_.isPlaying = true;
      audioStream_.cacheHits++;
      stats_.cacheHits++;
      spdlog::info("AudioDevice started");
    }
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("AudioDevice start failed: {}", e.what());
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_start_error.json");
  }
}

/**
 * @brief Stops audio playback.
 */
void AudioDevice::Stop() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (initialized_ && audioStream_.stream) {
      audioStream_.isPlaying = false;
      PaError err = Pa_StopStream(audioStream_.stream);
      if (err != paNoError) {
        spdlog::warn("PortAudio stop stream failed: {}", Pa_GetErrorText(err));
      }
      err = Pa_CloseStream(audioStream_.stream);
      if (err != paNoError) {
        spdlog::warn("PortAudio close stream failed: {}", Pa_GetErrorText(err));
      }
      audioStream_.stream = nullptr;
      while (!audioStream_.buffers.empty()) {
        audioStream_.buffers.pop();
      }
      audioStream_.cacheHits++;
      stats_.cacheHits++;
      stats_.bufferQueueSize = 0;
      spdlog::info("AudioDevice stopped");
    }
    Pa_Terminate();
    initialized_ = false;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("AudioDevice stop failed: {}", e.what());
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_stop_error.json");
  }
}

/**
 * @brief Queues an audio buffer for playback.
 */
void AudioDevice::QueueBuffer(const std::vector<float> &buffer) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (!initialized_) {
      spdlog::warn("Attempted to queue buffer on uninitialized audio device");
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return;
    }
    if (buffer.size() % audioStream_.numChannels != 0) {
      spdlog::warn("Invalid buffer size: {} not divisible by channels {}",
                   buffer.size(), audioStream_.numChannels);
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return;
    }
    audioStream_.buffers.push(buffer);
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    stats_.bufferQueueSize = audioStream_.buffers.size();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Queued audio buffer of size {}", buffer.size());
  } catch (const std::exception &e) {
    spdlog::error("Queue buffer failed: {}", e.what());
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_queue_error.json");
  }
}

/**
 * @brief Sets the volume level.
 */
void AudioDevice::SetVolume(float volume) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    audioStream_.volume = std::clamp(volume, 0.0f, 1.0f);
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Set audio volume to {:.2f}", audioStream_.volume);
  } catch (const std::exception &e) {
    spdlog::error("Set volume failed: {}", e.what());
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_volume_error.json");
  }
}

/**
 * @brief Reconfigures the stream with new parameters.
 */
bool AudioDevice::Reconfigure(int sampleRate, int numChannels) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (!initialized_) {
      spdlog::warn("Cannot reconfigure uninitialized audio device");
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return false;
    }
    if (numChannels < 1 || numChannels > MAX_CHANNELS) {
      spdlog::warn("Invalid channel count: {}", numChannels);
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return false;
    }
    Stop();
    audioStream_.sampleRate = sampleRate;
    audioStream_.numChannels = numChannels;
    stats_.numChannels = numChannels;
    bool success = Initialize(-1);
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("AudioDevice reconfigured: sampleRate={}, numChannels={}",
                 sampleRate, numChannels);
    return success;
  } catch (const std::exception &e) {
    spdlog::error("Reconfigure audio device failed: {}", e.what());
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_reconfigure_error.json");
    return false;
  }
}

/**
 * @brief Retrieves audio statistics.
 */
AudioDevice::Stats &AudioDevice::GetStats() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(streamMutex_);
  try {
    stats_.bufferQueueSize = audioStream_.buffers.size();
    stats_.numChannels = audioStream_.numChannels;
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return stats_;
  } catch (const std::exception &e) {
    spdlog::error("Get audio stats failed: {}", e.what());
    stats_.cacheMisses++;
    ExportCrashReport("audio_stats_error.json");
    return stats_;
  }
}

/**
 * @brief Saves the audio device state.
 */
void AudioDevice::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(streamMutex_);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(&audioStream_.sampleRate),
              sizeof(audioStream_.sampleRate));
    out.write(reinterpret_cast<const char *>(&audioStream_.numChannels),
              sizeof(audioStream_.numChannels));
    out.write(reinterpret_cast<const char *>(&audioStream_.volume),
              sizeof(audioStream_.volume));
    out.write(reinterpret_cast<const char *>(&audioStream_.isPlaying),
              sizeof(audioStream_.isPlaying));
    out.write(reinterpret_cast<const char *>(&audioStream_.bufferUnderruns),
              sizeof(audioStream_.bufferUnderruns));
    out.write(reinterpret_cast<const char *>(&audioStream_.cacheHits),
              sizeof(audioStream_.cacheHits));
    out.write(reinterpret_cast<const char *>(&audioStream_.cacheMisses),
              sizeof(audioStream_.cacheMisses));
    uint64_t bufferCount = audioStream_.buffers.size();
    out.write(reinterpret_cast<const char *>(&bufferCount),
              sizeof(bufferCount));
    auto tempBuffers = audioStream_.buffers;
    while (!tempBuffers.empty()) {
      const auto &buffer = tempBuffers.front();
      uint64_t bufferSize = buffer.size();
      out.write(reinterpret_cast<const char *>(&bufferSize),
                sizeof(bufferSize));
      out.write(reinterpret_cast<const char *>(buffer.data()),
                bufferSize * sizeof(float));
      tempBuffers.pop();
    }
    out.write(reinterpret_cast<const char *>(&stats_), sizeof(stats_));
    if (!out.good()) {
      throw std::runtime_error("Failed to write audio state");
    }
    const_cast<AudioStream &>(audioStream_).cacheHits++;
    const_cast<Stats &>(stats_).cacheHits++;
    auto end = std::chrono::steady_clock::now();
    const_cast<Stats &>(stats_).totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("AudioDevice state saved: {} buffers", bufferCount);
  } catch (const std::exception &e) {
    spdlog::error("AudioDevice SaveState failed: {}", e.what());
    const_cast<AudioStream &>(audioStream_).cacheMisses++;
    const_cast<Stats &>(stats_).cacheMisses++;
    ExportCrashReport("audio_savestate_error.json");
  }
}

/**
 * @brief Loads the audio device state.
 */
void AudioDevice::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported audio state version: {}", version);
      throw std::runtime_error("Invalid audio state version");
    }
    Stop();
    int sampleRate, numChannels;
    in.read(reinterpret_cast<char *>(&sampleRate), sizeof(sampleRate));
    in.read(reinterpret_cast<char *>(&numChannels), sizeof(numChannels));
    in.read(reinterpret_cast<char *>(&audioStream_.volume),
            sizeof(audioStream_.volume));
    in.read(reinterpret_cast<char *>(&audioStream_.isPlaying),
            sizeof(audioStream_.isPlaying));
    in.read(reinterpret_cast<char *>(&audioStream_.bufferUnderruns),
            sizeof(audioStream_.bufferUnderruns));
    in.read(reinterpret_cast<char *>(&audioStream_.cacheHits),
            sizeof(audioStream_.cacheHits));
    in.read(reinterpret_cast<char *>(&audioStream_.cacheMisses),
            sizeof(audioStream_.cacheMisses));
    uint64_t bufferCount;
    in.read(reinterpret_cast<char *>(&bufferCount), sizeof(bufferCount));
    while (!audioStream_.buffers.empty()) {
      audioStream_.buffers.pop();
    }
    for (uint64_t i = 0; i < bufferCount && in.good(); ++i) {
      uint64_t bufferSize;
      in.read(reinterpret_cast<char *>(&bufferSize), sizeof(bufferSize));
      std::vector<float> buffer(bufferSize);
      in.read(reinterpret_cast<char *>(buffer.data()),
              bufferSize * sizeof(float));
      audioStream_.buffers.push(std::move(buffer));
    }
    in.read(reinterpret_cast<char *>(&stats_), sizeof(stats_));
    if (!in.good()) {
      throw std::runtime_error("Failed to read audio state");
    }
    audioStream_.sampleRate = sampleRate;
    audioStream_.numChannels = numChannels;
    stats_.numChannels = numChannels;
    stats_.bufferQueueSize = audioStream_.buffers.size();
    if (initialized_) {
      Reconfigure(sampleRate, numChannels);
    }
    if (audioStream_.isPlaying) {
      Start();
    }
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("AudioDevice state loaded: {} buffers", bufferCount);
  } catch (const std::exception &e) {
    spdlog::error("AudioDevice LoadState failed: {}", e.what());
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    ExportCrashReport("audio_loadstate_error.json");
  }
}

/**
 * @brief Constructs the audio system.
 */
PS4Audio::PS4Audio(PS4MMU &memory)
    : m_memory(memory), m_audio3DMode(Audio3DMode::DISABLED) {
  auto start = std::chrono::steady_clock::now();
  std::fill(m_listenerPosition, m_listenerPosition + 3, 0.0f);
  std::fill(m_listenerOrientation, m_listenerOrientation + 6, 0.0f);
  spdlog::info("PS4Audio constructed");
  auto end = std::chrono::steady_clock::now();
  m_device.GetStats().totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the audio system.
 */
bool PS4Audio::Initialize() {
  return Initialize(true); // Default to enabled
}

/**
 * @brief Initializes the audio system with optional enable/disable.
 */
bool PS4Audio::Initialize(bool enableAudio) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (!enableAudio) {
      spdlog::info("Audio initialization skipped (disabled by configuration)");
      // Initialize in null mode - no actual audio but still functional
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(
              std::chrono::steady_clock::now() - start).count();
      return true;
    }

    bool success = m_device.Initialize(-1);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Audio initialized: success={}", success);
    return success;
  } catch (const std::exception &e) {
    spdlog::error("PS4Audio initialization failed: {}", e.what());
    m_stats.errorCount++;
    ExportCrashReport("ps4audio_init_error.json");
    return false;
  }
}

/**
 * @brief Shuts down the audio system.
 */
void PS4Audio::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  try {
    m_device.Stop();
    spdlog::info("PS4Audio shutdown");
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("PS4Audio shutdown failed: {}", e.what());
    m_stats.errorCount++;
    ExportCrashReport("ps4audio_shutdown_error.json");
  }
}

/**
 * @brief Processes audio samples from MMU.
 */
void PS4Audio::ProcessAudio(size_t samples, uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t pid = 1;
    size_t bufferSize = samples * m_device.GetNumChannels() * sizeof(float);
    std::vector<float> buffer(samples * m_device.GetNumChannels());
    if (!m_memory.ReadVirtual(address, buffer.data(), bufferSize, pid)) {
      spdlog::warn("Failed to read audio data: addr=0x{:x}", address);
      std::fill(buffer.begin(), buffer.end(), 0.0f);
      m_stats.errorCount++;
    }
    m_device.QueueBuffer(buffer);
    m_stats.samplesProcessed += samples;
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("Process audio failed: {}", e.what());
    m_stats.errorCount++;
    ExportCrashReport("ps4audio_process_error.json");
  }
}

/**
 * @brief Processes enhanced audio buffer with DSP effects.
 */
bool PS4Audio::ProcessEnhancedAudio(const EnhancedAudioBuffer &buffer) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_audioMutex);
  try {
    std::vector<float> processedData = buffer.data;

    if (buffer.is3D && m_audio3DMode != Audio3DMode::DISABLED) {
      ProcessAudio3D(const_cast<EnhancedAudioBuffer &>(buffer),
                     m_listenerPosition, m_listenerOrientation, m_audio3DMode);
    }

    for (const auto &effect : m_dspEffects) {
      if (effect.enabled) {
        ApplyDSPEffect(processedData, effect);
      }
    }

    m_device.QueueBuffer(processedData);

    m_stats.samplesProcessed += processedData.size();
    m_stats.dspOperations++;
    m_stats.cacheHits++;

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessEnhancedAudio failed: {}", e.what());
    ExportCrashReport("ps4audio_enhanced_error.json");
    return false;
  }
}

/**
 * @brief Applies DSP effect to audio data.
 */
bool PS4Audio::ApplyDSPEffect(std::vector<float> &audioData,
                              const DSPEffectParams &params) {
  try {
    switch (params.type) {
    case DSPEffectType::REVERB:
      return ProcessReverb(audioData, params.intensity, params.feedback);
    case DSPEffectType::DELAY:
      return ProcessDelay(audioData, params.frequency, params.feedback);
    case DSPEffectType::CHORUS:
      return ProcessChorus(audioData, params.frequency, params.intensity);
    case DSPEffectType::COMPRESSOR:
      return ProcessCompressor(audioData, params.intensity, params.feedback);
    case DSPEffectType::EQUALIZER: {
      std::vector<float> bands = {params.intensity, params.frequency,
                                  params.feedback};
      return ProcessEqualizer(audioData, bands);
    }
    case DSPEffectType::LIMITER:
      return ProcessLimiter(audioData, params.intensity);
    default:
      spdlog::warn("Unknown DSP effect type: {}",
                   static_cast<int>(params.type));
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("ApplyDSPEffect failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes reverb effect on audio data.
 */
bool PS4Audio::ProcessReverb(std::vector<float> &audioData, float intensity,
                             float feedback) {
  try {
    if (audioData.empty() || intensity <= 0.0f)
      return true;

    const size_t delayLength = static_cast<size_t>(48000 * 0.05f);
    static std::vector<float> delayBuffer(delayLength, 0.0f);
    static size_t delayIndex = 0;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float delayed = delayBuffer[delayIndex];
      float reverb = delayed * feedback * intensity;

      delayBuffer[delayIndex] = audioData[i] + reverb * 0.3f;
      audioData[i] = audioData[i] * (1.0f - intensity * 0.5f) + reverb;

      delayIndex = (delayIndex + 1) % delayLength;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessReverb failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes delay effect on audio data.
 */
bool PS4Audio::ProcessDelay(std::vector<float> &audioData, float delayTime,
                            float feedback) {
  try {
    if (audioData.empty() || delayTime <= 0.0f)
      return true;

    const size_t delayLength =
        static_cast<size_t>(48000 * std::min(delayTime, 1.0f));
    static std::vector<float> delayBuffer(delayLength, 0.0f);
    static size_t delayIndex = 0;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float delayed = delayBuffer[delayIndex];

      delayBuffer[delayIndex] = audioData[i] + delayed * feedback;
      audioData[i] = audioData[i] + delayed * 0.5f;

      delayIndex = (delayIndex + 1) % delayLength;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessDelay failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes chorus effect on audio data.
 */
bool PS4Audio::ProcessChorus(std::vector<float> &audioData, float rate,
                             float depth) {
  try {
    if (audioData.empty() || depth <= 0.0f)
      return true;

    const size_t maxDelay = static_cast<size_t>(48000 * 0.02f);
    static std::vector<float> delayBuffer(maxDelay, 0.0f);
    static size_t writeIndex = 0;
    static float lfoPhase = 0.0f;

    const float sampleRate = 48000.0f;
    const float lfoIncrement = 2.0f * M_PI * rate / sampleRate;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float lfo = std::sin(lfoPhase) * depth * 0.5f + 0.5f;
      float delayTime = lfo * (maxDelay - 1);

      size_t readIndex =
          static_cast<size_t>(writeIndex - delayTime + maxDelay) % maxDelay;
      float delayedSample = delayBuffer[readIndex];

      delayBuffer[writeIndex] = audioData[i];
      audioData[i] = audioData[i] * 0.7f + delayedSample * 0.3f;

      writeIndex = (writeIndex + 1) % maxDelay;
      lfoPhase += lfoIncrement;
      if (lfoPhase >= 2.0f * M_PI)
        lfoPhase -= 2.0f * M_PI;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessChorus failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes compressor effect on audio data.
 */
bool PS4Audio::ProcessCompressor(std::vector<float> &audioData, float threshold,
                                 float ratio) {
  try {
    if (audioData.empty() || threshold >= 1.0f || ratio <= 1.0f)
      return true;

    static float envelope = 0.0f;
    const float attack = 0.003f;
    const float release = 0.1f;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float sample = std::abs(audioData[i]);

      if (sample > envelope) {
        envelope = envelope + (sample - envelope) * attack;
      } else {
        envelope = envelope + (sample - envelope) * release;
      }

      if (envelope > threshold) {
        float excess = envelope - threshold;
        float compressedExcess = excess / ratio;
        float gain = (threshold + compressedExcess) / envelope;
        audioData[i] *= gain;
      }
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessCompressor failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes equalizer effect on audio data.
 */
bool PS4Audio::ProcessEqualizer(std::vector<float> &audioData,
                                const std::vector<float> &bands) {
  try {
    if (audioData.empty() || bands.empty())
      return true;

    static float lowState = 0.0f, midState = 0.0f, highState = 0.0f;

    const float lowGain = bands.size() > 0 ? bands[0] : 1.0f;
    const float midGain = bands.size() > 1 ? bands[1] : 1.0f;
    const float highGain = bands.size() > 2 ? bands[2] : 1.0f;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float sample = audioData[i];

      lowState = lowState * 0.99f + sample * 0.01f;
      float low = lowState * lowGain;

      midState = midState * 0.9f + (sample - lowState) * 0.1f;
      float mid = midState * midGain;

      highState = sample - lowState - midState;
      float high = highState * highGain;

      audioData[i] = low + mid + high;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessEqualizer failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes limiter effect on audio data.
 */
bool PS4Audio::ProcessLimiter(std::vector<float> &audioData, float threshold) {
  try {
    if (audioData.empty() || threshold >= 1.0f)
      return true;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float sample = audioData[i];

      if (std::abs(sample) > threshold) {
        audioData[i] = sample > 0.0f ? threshold : -threshold;
      }
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessLimiter failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Applies HRTF processing for 3D audio.
 */
bool PS4Audio::ApplyHRTF(std::vector<float> &audioData,
                         const float sourcePos[3]) {
  try {
    if (audioData.empty())
      return true;

    float dx = sourcePos[0] - m_listenerPosition[0];
    float dy = sourcePos[1] - m_listenerPosition[1];
    float dz = sourcePos[2] - m_listenerPosition[2];

    float distance = std::sqrt(dx * dx + dy * dy + dz * dz);
    float azimuth = std::atan2(dz, dx);

    float leftGain = 0.5f + 0.5f * std::cos(azimuth);
    float rightGain = 0.5f - 0.5f * std::cos(azimuth);

    float attenuation = 1.0f / (1.0f + distance * 0.1f);
    leftGain *= attenuation;
    rightGain *= attenuation;

    for (size_t i = 0; i < audioData.size(); i += 2) {
      if (i + 1 < audioData.size()) {
        audioData[i] *= leftGain;
        audioData[i + 1] *= rightGain;
      }
    }

    m_stats.audio3DOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ApplyHRTF failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Applies surround sound processing.
 */
bool PS4Audio::ApplySurroundProcessing(std::vector<float> &audioData,
                                       uint32_t channels) {
  try {
    if (audioData.empty() || channels < 2)
      return true;

    if (channels == 6) {
      std::vector<float> processed;
      processed.reserve(audioData.size() * 3);

      for (size_t i = 0; i < audioData.size(); i += 2) {
        if (i + 1 < audioData.size()) {
          float left = audioData[i];
          float right = audioData[i + 1];
          float center = (left + right) * 0.5f;

          processed.push_back(left);
          processed.push_back(right);
          processed.push_back(center);
          processed.push_back(0.0f);
          processed.push_back(left * 0.3f);
          processed.push_back(right * 0.3f);
        }
      }
      audioData = std::move(processed);
    }

    m_stats.audio3DOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ApplySurroundProcessing failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Applies binaural processing for headphone 3D audio.
 */
bool PS4Audio::ApplyBinauralProcessing(std::vector<float> &audioData) {
  try {
    if (audioData.empty())
      return true;

    const float crossfeedGain = 0.3f;
    const float directGain = 0.7f;

    for (size_t i = 0; i < audioData.size(); i += 2) {
      if (i + 1 < audioData.size()) {
        float left = audioData[i];
        float right = audioData[i + 1];

        audioData[i] = left * directGain + right * crossfeedGain;
        audioData[i + 1] = right * directGain + left * crossfeedGain;
      }
    }

    m_stats.audio3DOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ApplyBinauralProcessing failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes 3D audio with specified mode.
 */
bool PS4Audio::ProcessAudio3D(EnhancedAudioBuffer &buffer,
                              const float listenerPos[3],
                              const float listenerOrientation[6],
                              Audio3DMode mode) {
  try {
    switch (mode) {
    case Audio3DMode::HRTF:
      return ApplyHRTF(buffer.data, buffer.position);
    case Audio3DMode::SURROUND:
      return ApplySurroundProcessing(buffer.data, buffer.channels);
    case Audio3DMode::BINAURAL:
      return ApplyBinauralProcessing(buffer.data);
    case Audio3DMode::DISABLED:
    default:
      return true;
    }
  } catch (const std::exception &e) {
    spdlog::error("ProcessAudio3D failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Decompresses ADPCM audio format.
 */
bool PS4Audio::DecompressADPCM(const std::vector<uint8_t> &compressed,
                               std::vector<float> &output) {
  try {
    if (compressed.empty())
      return false;

    output.clear();
    output.reserve(compressed.size() * 2);

    int16_t predictedValue = 0;
    int stepIndex = 0;
    const int stepTable[] = {7,  8,  9,  10, 11, 12, 13, 14,
                             16, 17, 19, 21, 23, 25, 28, 31};

    for (size_t i = 0; i < compressed.size(); ++i) {
      uint8_t nibbles = compressed[i];

      for (int j = 0; j < 2; ++j) {
        uint8_t nibble =
            (j == 0) ? (nibbles & 0x0F) : ((nibbles >> 4) & 0x0F);

        int step = stepTable[std::min(stepIndex, 15)];
        int diff = step >> 3;

        if (nibble & 4)
          diff += step;
        if (nibble & 2)
          diff += step >> 1;
        if (nibble & 1)
          diff += step >> 2;

        if (nibble & 8) {
          predictedValue -= diff;
        } else {
          predictedValue += diff;
        }

        predictedValue = std::clamp(
            predictedValue, static_cast<int16_t>(-32768),
            static_cast<int16_t>(32767));
        output.push_back(static_cast<float>(predictedValue) / 32768.0f);

        stepIndex += (nibble & 8) ? -1 : 1;
        stepIndex = std::clamp(stepIndex, 0, 15);
      }
    }

    m_stats.compressionOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DecompressADPCM failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

bool PS4Audio::DecompressAT9(const std::vector<uint8_t> &compressed,
                             std::vector<float> &output) {
  const AVCodec *codec = nullptr;
  AVCodecContext *codecCtx = nullptr;
  AVPacket *packet = nullptr;
  AVFrame *frame = nullptr;
  SwrContext *swrCtx = nullptr;
  int ret = 0;

  output.clear();

  codec = avcodec_find_decoder(AV_CODEC_ID_ATRAC9);
  if (!codec) {
    spdlog::error("ATRAC9 codec not found");
    m_stats.errorCount++;
    return false;
  }

  codecCtx = avcodec_alloc_context3(codec);
  if (!codecCtx) {
    spdlog::error("Failed to allocate codec context");
    m_stats.errorCount++;
    goto end;
  }

  if ((ret = avcodec_open2(codecCtx, codec, nullptr)) < 0) {
    spdlog::error("Failed to open codec: {}", ret);
    m_stats.errorCount++;
    goto end;
  }

  packet = av_packet_alloc();
  if (!packet) {
    spdlog::error("Failed to allocate packet");
    m_stats.errorCount++;
    goto end;
  }

  frame = av_frame_alloc();
  if (!frame) {
    spdlog::error("Failed to allocate frame");
    m_stats.errorCount++;
    goto end;
  }

  packet->data = const_cast<uint8_t *>(compressed.data());
  packet->size = static_cast<int>(compressed.size());

  ret = avcodec_send_packet(codecCtx, packet);
  if (ret < 0) {
    spdlog::error("Error sending packet to decoder: {}", ret);
    m_stats.errorCount++;
    goto end;
  }

  ret = avcodec_receive_frame(codecCtx, frame);
  if (ret < 0) {
    spdlog::error("Error receiving frame from decoder: {}", ret);
    m_stats.errorCount++;
    goto end;
  }

  {
    AVChannelLayout out_ch_layout;
    av_channel_layout_default(&out_ch_layout, 2); // Stereo

    ret = swr_alloc_set_opts2(&swrCtx, &out_ch_layout, AV_SAMPLE_FMT_FLT,
                              frame->sample_rate, &frame->ch_layout,
                              static_cast<AVSampleFormat>(frame->format),
                              frame->sample_rate, 0, nullptr);
  }

  if (!swrCtx || ret < 0 || swr_init(swrCtx) < 0) {
    spdlog::error("Failed to initialize resampler");
    m_stats.errorCount++;
    goto end;
  }

  {
    int outSamples = av_rescale_rnd(
        swr_get_delay(swrCtx, frame->sample_rate) + frame->nb_samples,
        frame->sample_rate, frame->sample_rate, AV_ROUND_UP);

    std::vector<float> resampledData(outSamples * frame->ch_layout.nb_channels);

    uint8_t *outData[1] = {reinterpret_cast<uint8_t *>(resampledData.data())};
    int convertedSamples =
        swr_convert(swrCtx, outData, outSamples,
                    (const uint8_t **)frame->data, frame->nb_samples);
    if (convertedSamples < 0) {
      spdlog::error("Error during resampling");
      m_stats.errorCount++;
      goto end;
    }

    output.assign(resampledData.begin(),
                  resampledData.begin() +
                      convertedSamples * frame->ch_layout.nb_channels);
  }

  m_stats.compressionOperations++;

end:
  if (frame)
    av_frame_free(&frame);
  if (packet)
    av_packet_free(&packet);
  if (codecCtx)
    avcodec_free_context(&codecCtx);
  if (swrCtx)
    swr_free(&swrCtx);

  return ret >= 0;
}

/**
 * @brief Decompresses MP3 audio format (placeholder).
 */
bool PS4Audio::DecompressMP3(const std::vector<uint8_t> &compressed,
                             std::vector<float> &output) {
  try {
    spdlog::warn("MP3 decompression not implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 8, 0.0f);
    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressMP3 failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Decompresses AAC audio format (placeholder).
 */
bool PS4Audio::DecompressAAC(const std::vector<uint8_t> &compressed,
                             std::vector<float> &output) {
  try {
    spdlog::warn("AAC decompression not implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 6, 0.0f);
    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressAAC failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Decompresses Opus audio format (placeholder).
 */
bool PS4Audio::DecompressOpus(const std::vector<uint8_t> &compressed,
                              std::vector<float> &output) {
  try {
    spdlog::warn("Opus decompression not implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 10, 0.0f);
    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressOpus failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Converts PCM format to float.
 */
bool PS4Audio::ConvertPCMFormat(const std::vector<uint8_t> &input,
                                PS4AudioFormat inputFormat,
                                std::vector<float> &output) {
  try {
    output.clear();

    switch (inputFormat) {
    case PS4AudioFormat::PCM_S16: {
      output.reserve(input.size() / 2);
      for (size_t i = 0; i < input.size(); i += 2) {
        if (i + 1 < input.size()) {
          int16_t sample =
              static_cast<int16_t>(input[i] | (input[i + 1] << 8));
          output.push_back(static_cast<float>(sample) / 32768.0f);
        }
      }
      break;
    }
    case PS4AudioFormat::PCM_S24: {
      output.reserve(input.size() / 3);
      for (size_t i = 0; i < input.size(); i += 3) {
        if (i + 2 < input.size()) {
          int32_t sample = static_cast<int32_t>(
              input[i] | (input[i + 1] << 8) | (input[i + 2] << 16));
          if (sample & 0x800000)
            sample |= 0xFF000000;
          output.push_back(static_cast<float>(sample) / 8388608.0f);
        }
      }
      break;
    }
    case PS4AudioFormat::PCM_F32: {
      output.reserve(input.size() / 4);
      for (size_t i = 0; i < input.size(); i += 4) {
        if (i + 3 < input.size()) {
          float sample;
          std::memcpy(&sample, &input[i], sizeof(float));
          output.push_back(sample);
        }
      }
      break;
    }
    default:
      spdlog::error("Unsupported PCM format: {}",
                    static_cast<int>(inputFormat));
      m_stats.errorCount++;
      return false;
    }

    m_stats.compressionOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ConvertPCMFormat failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Decompresses audio based on format.
 */
bool PS4Audio::DecompressAudio(const std::vector<uint8_t> &compressed,
                               PS4AudioFormat format,
                               std::vector<float> &output) {
  switch (format) {
  case PS4AudioFormat::PCM_S16:
  case PS4AudioFormat::PCM_S24:
  case PS4AudioFormat::PCM_F32:
    return ConvertPCMFormat(compressed, format, output);
  case PS4AudioFormat::ADPCM:
    return DecompressADPCM(compressed, output);
  case PS4AudioFormat::AT9:
    return DecompressAT9(compressed, output);
  case PS4AudioFormat::MP3:
    return DecompressMP3(compressed, output);
  case PS4AudioFormat::AAC:
    return DecompressAAC(compressed, output);
  case PS4AudioFormat::OPUS:
    return DecompressOpus(compressed, output);
  default:
    spdlog::error("Unknown audio format: {}", static_cast<int>(format));
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Compresses audio to specified format (placeholder).
 */
bool PS4Audio::CompressAudio(const std::vector<float> &input,
                             PS4AudioFormat format,
                             std::vector<uint8_t> &output) {
  try {
    spdlog::warn("Audio compression not implemented - returning empty");
    output.clear();
    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("CompressAudio failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Processes multi-channel audio mixing.
 */
bool PS4Audio::ProcessMultiChannelAudio(
    const std::vector<EnhancedAudioBuffer> &channels,
    std::vector<float> &mixedOutput) {
  try {
    if (channels.empty())
      return false;

    size_t maxSize = 0;
    for (const auto &channel : channels) {
      maxSize = std::max(maxSize, channel.data.size());
    }

    mixedOutput.clear();
    mixedOutput.resize(maxSize, 0.0f);

    for (const auto &channel : channels) {
      float weight = static_cast<float>(channel.priority) / 100.0f;
      weight = std::clamp(weight, 0.1f, 1.0f);

      for (size_t i = 0; i < std::min(channel.data.size(), maxSize); ++i) {
        mixedOutput[i] += channel.data[i] * weight;
      }
    }

    float maxSample = 0.0f;
    for (float sample : mixedOutput) {
      maxSample = std::max(maxSample, std::abs(sample));
    }

    if (maxSample > 1.0f) {
      float normFactor = 1.0f / maxSample;
      for (float &sample : mixedOutput) {
        sample *= normFactor;
      }
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessMultiChannelAudio failed: {}", e.what());
    m_stats.errorCount++;
    return false;
  }
}

/**
 * @brief Gets enhanced audio statistics.
 */
PS4Audio::Stats PS4Audio::GetStats() const {
  std::shared_lock<std::shared_mutex> lock(m_audioMutex);
  return m_stats;
}

/**
 * @brief Saves the PS4Audio system state.
 */
void PS4Audio::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_audioMutex);
  try {
    // Save version for compatibility
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Save PS4Audio-specific state
    out.write(reinterpret_cast<const char *>(&m_audio3DMode),
              sizeof(m_audio3DMode));
    out.write(reinterpret_cast<const char *>(m_listenerPosition),
              sizeof(m_listenerPosition));
    out.write(reinterpret_cast<const char *>(m_listenerOrientation),
              sizeof(m_listenerOrientation));

    // Save DSP effects
    uint64_t effectCount = m_dspEffects.size();
    out.write(reinterpret_cast<const char *>(&effectCount), sizeof(effectCount));
    for (const auto &effect : m_dspEffects) {
      out.write(reinterpret_cast<const char *>(&effect), sizeof(effect));
    }

    // Save statistics
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    // Delegate to AudioDevice for device-specific state
    m_device.SaveState(out);

    if (!out.good()) {
      throw std::runtime_error("Failed to write PS4Audio state");
    }

    auto end = std::chrono::steady_clock::now();
    const_cast<Stats &>(m_stats).totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::info("PS4Audio state saved: {} DSP effects", effectCount);
  } catch (const std::exception &e) {
    spdlog::error("PS4Audio SaveState failed: {}", e.what());
    const_cast<Stats &>(m_stats).errorCount++;
    ExportCrashReport("ps4audio_savestate_error.json");
    throw;
  }
}

/**
 * @brief Loads the PS4Audio system state.
 */
void PS4Audio::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_audioMutex);
  try {
    // Load version for compatibility
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported PS4Audio state version: {}", version);
      throw std::runtime_error("Invalid PS4Audio state version");
    }

    // Load PS4Audio-specific state
    in.read(reinterpret_cast<char *>(&m_audio3DMode), sizeof(m_audio3DMode));
    in.read(reinterpret_cast<char *>(m_listenerPosition),
            sizeof(m_listenerPosition));
    in.read(reinterpret_cast<char *>(m_listenerOrientation),
            sizeof(m_listenerOrientation));

    // Load DSP effects
    uint64_t effectCount;
    in.read(reinterpret_cast<char *>(&effectCount), sizeof(effectCount));
    m_dspEffects.clear();
    m_dspEffects.resize(effectCount);
    for (auto &effect : m_dspEffects) {
      in.read(reinterpret_cast<char *>(&effect), sizeof(effect));
    }

    // Load statistics
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    // Delegate to AudioDevice for device-specific state
    m_device.LoadState(in);

    if (!in.good()) {
      throw std::runtime_error("Failed to read PS4Audio state");
    }

    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::info("PS4Audio state loaded: {} DSP effects", effectCount);
  } catch (const std::exception &e) {
    spdlog::error("PS4Audio LoadState failed: {}", e.what());
    m_stats.errorCount++;
    ExportCrashReport("ps4audio_loadstate_error.json");
    throw;
  }
}

} // namespace ps4
